import { createTool } from '@mastra/core/tools';
import { z } from 'zod';
import { searchFaqs } from '../../../services/weaviate/faq.service';

/**
 * Công cụ lấy các câu hỏi thường gặp từ Weaviate
 * Sử dụng Weaviate để tìm kiếm FAQ dựa trên chủ đề
 */
export const getFaqsTool = createTool({
  id: 'get_faqs',
  description: 'L<PERSON>y các câu hỏi thường gặp theo chủ đề',
  inputSchema: z.object({
    topic: z.array(z.string()).describe('Chủ đề câu hỏi (shipping, returns, payment, sizing, etc.)')  
  }),
  execute: async ({ context, runtimeContext }) => {
    try {
      console.log('Đang lấy câu hỏi thường gặp về:', context.topic.join(', '));

      // Lấy tenant_id từ runtime context
      const tenant_id = runtimeContext.get("tenant_id");
      const bot_id = runtimeContext.get("bot_id");

      if (!tenant_id) {
        return {
          faqs: [],
          error: "Thiếu thông tin tenant_id trong runtime context",
        };
      }

      // Tìm kiếm FAQ từ Weaviate
      const result = await searchFaqs(
        context.topic.join(','),
        tenant_id.toString(),
        3,
        bot_id ? bot_id.toString() : undefined
      );

      if (!result.success) {
        console.error(`Không tìm thấy FAQ với chủ đề ${context.topic.join(', ')}`);
        return {
          faqs: [],
          error: result.message,
          topic: context.topic
        };
      }

      // Chuyển đổi kết quả từ Weaviate sang định dạng FAQ
      const faqs = result.data.objects.map((obj: any) => ({
        question: obj.properties?.topic,
        answer: obj.properties?.content
      }));

      return {
        faqs: faqs,
        topic: context.topic
      };
    } catch (error: any) {
      console.error("Lỗi khi lấy FAQ:", error);
      return {
        faqs: [],
        error: error instanceof Error ? error.message : "Lỗi không xác định",
        topic: context.topic
      };
    }
  }
});
